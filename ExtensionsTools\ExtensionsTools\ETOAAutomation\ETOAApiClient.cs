using System;
using System.Threading.Tasks;
using Flurl.Http;
using ExtensionsTools.ETOAAutomation.Models;
using ET;

namespace ExtensionsTools.ETOAAutomation
{
    /// <summary>
    /// 基于Flurl.Http的API交互客户端，处理所有HTTP请求
    /// </summary>
    public class ETOAApiClient
    {
        #region 私有字段
        private readonly IFlurlClient _httpClient;
        private ETOALoginInfo _loginInfo;
        #endregion

        #region 公共属性
        /// <summary>
        /// API基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 是否已认证
        /// </summary>
        public bool IsAuthenticated => _loginInfo != null && _loginInfo.IsSuccess;
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化API客户端
        /// </summary>
        /// <param name="baseUrl">API基础URL</param>
        public ETOAApiClient(string baseUrl)
        {
            BaseUrl = baseUrl ?? throw new ArgumentNullException(nameof(baseUrl));
            _httpClient = new FlurlClient(BaseUrl);
            ConfigureHttpClient();
            ETLogManager.Info($"ETOAApiClient初始化完成，BaseUrl: {baseUrl}");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 配置HTTP客户端
        /// </summary>
        private void ConfigureHttpClient()
        {
            _httpClient.Settings.Timeout = TimeSpan.FromSeconds(TimeoutSeconds);
            _httpClient.Settings.AllowedHttpStatusRange = "200-299,400-499";
        }

        /// <summary>
        /// 应用认证信息到请求
        /// </summary>
        /// <param name="request">HTTP请求</param>
        /// <returns>配置后的请求</returns>
        private IFlurlRequest ApplyAuthentication(IFlurlRequest request)
        {
            if (_loginInfo?.Cookies != null)
            {
                foreach (var cookie in _loginInfo.Cookies)
                {
                    request = request.WithCookie(cookie.Key, cookie.Value);
                }
            }

            if (_loginInfo?.Headers != null)
            {
                foreach (var header in _loginInfo.Headers)
                {
                    request = request.WithHeader(header.Key, header.Value);
                }
            }

            return request;
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 设置认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        public void SetAuthenticationInfo(ETOALoginInfo loginInfo)
        {
            _loginInfo = loginInfo ?? throw new ArgumentNullException(nameof(loginInfo));
            ETLogManager.Info("API客户端认证信息设置完成");
        }

        /// <summary>
        /// GET请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <returns>响应数据</returns>
        public async Task<T> GetAsync<T>(string endpoint)
        {
            try
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);
                
                var response = await request.GetAsync();
                return await response.GetJsonAsync<T>();
            }
            catch (FlurlHttpException ex)
            {
                ETLogManager.Error($"GET请求失败: {endpoint}", ex);
                throw new ETException($"GET请求失败: {ex.Message}", "GetAsync", ex);
            }
        }

        /// <summary>
        /// POST请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <returns>响应数据</returns>
        public async Task<T> PostAsync<T>(string endpoint, object data)
        {
            try
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);
                
                var response = await request.PostJsonAsync(data);
                return await response.GetJsonAsync<T>();
            }
            catch (FlurlHttpException ex)
            {
                ETLogManager.Error($"POST请求失败: {endpoint}", ex);
                throw new ETException($"POST请求失败: {ex.Message}", "PostAsync", ex);
            }
        }

        /// <summary>
        /// PUT请求
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">请求数据</param>
        /// <returns>响应数据</returns>
        public async Task<T> PutAsync<T>(string endpoint, object data)
        {
            try
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);
                
                var response = await request.PutJsonAsync(data);
                return await response.GetJsonAsync<T>();
            }
            catch (FlurlHttpException ex)
            {
                throw new Exception($"PUT请求失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// DELETE请求
        /// </summary>
        /// <param name="endpoint">API端点</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteAsync(string endpoint)
        {
            try
            {
                var request = _httpClient.Request(endpoint);
                request = ApplyAuthentication(request);
                
                var response = await request.DeleteAsync();
                return response.ResponseMessage.IsSuccessStatusCode;
            }
            catch (FlurlHttpException ex)
            {
                throw new Exception($"DELETE请求失败: {ex.Message}", ex);
            }
        }
        #endregion
    }
}
