using System;
using System.Threading.Tasks;
using ExtensionsTools.ETOAAutomation.Models;
using ET;

namespace ExtensionsTools.ETOAAutomation
{
    /// <summary>
    /// OA系统自动化客户端主类，整合所有功能模块，提供统一接口
    /// </summary>
    public class ETOAClient : IDisposable
    {
        #region 私有字段
        private ETOALoginBrowser _loginBrowser;
        private ETOAApiClient _apiClient;
        private ETOASessionManager _sessionManager;
        private ETOAFileUploader _fileUploader;
        private ETOASimulationBrowser _simulationBrowser;
        private ETIniFile _configFile;
        private bool _disposed = false;
        #endregion

        #region 公共属性
        /// <summary>
        /// OA系统基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 登录状态
        /// </summary>
        public bool IsLoggedIn { get; private set; }

        /// <summary>
        /// 登录信息
        /// </summary>
        public ETOALoginInfo LoginInfo { get; private set; }
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化ETOAClient
        /// </summary>
        /// <param name="baseUrl">OA系统基础URL</param>
        public ETOAClient(string baseUrl)
        {
            BaseUrl = baseUrl ?? throw new ArgumentNullException(nameof(baseUrl));
            InitializeComponents();
            ETLogManager.Info($"ETOAClient初始化完成，BaseUrl: {baseUrl}");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化组件
        /// </summary>
        private void InitializeComponents()
        {
            try
            {
                // 初始化配置文件
                string configPath = System.IO.Path.Combine(
                    System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location),
                    "Config", "ETOAAutomation.ini");
                _configFile = new ETIniFile(configPath);

                // 初始化核心组件
                _apiClient = new ETOAApiClient(BaseUrl);
                _sessionManager = new ETOASessionManager(_apiClient);
                _fileUploader = new ETOAFileUploader(_apiClient);

                ETLogManager.Info("ETOAClient组件初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("ETOAClient组件初始化失败", ex);
                throw new ETException("ETOAClient组件初始化失败", "InitializeComponents", ex);
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 登录OA系统
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                ETLogManager.Info($"开始登录OA系统，用户名: {username}");

                _loginBrowser = new ETOALoginBrowser($"{BaseUrl}/login");
                LoginInfo = await _loginBrowser.ShowLoginDialogAsync();

                if (LoginInfo != null && LoginInfo.IsSuccess)
                {
                    _apiClient.SetAuthenticationInfo(LoginInfo);
                    IsLoggedIn = true;
                    await _sessionManager.StartSessionMonitoringAsync();

                    // 保存登录配置
                    SaveLoginConfig(username);

                    ETLogManager.Info("OA系统登录成功");
                    return true;
                }

                ETLogManager.Warn("OA系统登录失败");
                return false;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("OA系统登录异常", ex);
                throw new ETException($"登录失败: {ex.Message}", "LoginAsync", ex);
            }
        }

        /// <summary>
        /// 获取API数据
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <returns>API响应数据</returns>
        public async Task<T> GetApiDataAsync<T>(string endpoint)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            return await _apiClient.GetAsync<T>(endpoint);
        }

        /// <summary>
        /// 提交API数据
        /// </summary>
        /// <typeparam name="T">返回数据类型</typeparam>
        /// <param name="endpoint">API端点</param>
        /// <param name="data">提交的数据</param>
        /// <returns>API响应数据</returns>
        public async Task<T> PostApiDataAsync<T>(string endpoint, object data)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            return await _apiClient.PostAsync<T>(endpoint, data);
        }

        /// <summary>
        /// 上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
        {
            if (!IsLoggedIn)
                throw new InvalidOperationException("请先登录系统");

            return await _fileUploader.UploadFileAsync(endpoint, filePath, formData);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _sessionManager?.StopSessionMonitoring();
                _loginBrowser?.Close();
                _simulationBrowser?.Close();
                _disposed = true;
                ETLogManager.Info("ETOAClient资源释放完成");
            }
        }

        /// <summary>
        /// 保存登录配置
        /// </summary>
        /// <param name="username">用户名</param>
        private void SaveLoginConfig(string username)
        {
            try
            {
                _configFile.IniWriteValue("Login", "LastUsername", username);
                _configFile.IniWriteValue("Login", "LastLoginTime", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue("Login", "BaseUrl", BaseUrl);
                ETLogManager.Debug("登录配置保存成功");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("保存登录配置失败", ex);
            }
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>配置值</returns>
        public string GetConfig(string section, string key, string defaultValue = "")
        {
            try
            {
                return _configFile.IniReadValue(section, key, defaultValue);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"读取配置失败: {section}.{key}", ex);
                return defaultValue;
            }
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="section">配置节</param>
        /// <param name="key">配置键</param>
        /// <param name="value">配置值</param>
        public void SetConfig(string section, string key, string value)
        {
            try
            {
                _configFile.IniWriteValue(section, key, value);
                ETLogManager.Debug($"配置设置成功: {section}.{key} = {value}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"设置配置失败: {section}.{key}", ex);
            }
        }
        #endregion
    }
}
