using System;
using System.Threading;
using System.Threading.Tasks;
using ExtensionsTools.ETOAAutomation.Models;
using ET;

namespace ExtensionsTools.ETOAAutomation
{
    /// <summary>
    /// 会话状态管理器，负责维护登录状态和定期刷新
    /// </summary>
    public class ETOASessionManager
    {
        #region 私有字段
        private readonly ETOAApiClient _apiClient;
        private Timer _heartbeatTimer;
        private bool _isMonitoring = false;
        private DateTime _lastHeartbeat = DateTime.MinValue;
        private int _heartbeatInterval = 300; // 默认5分钟
        #endregion

        #region 公共属性
        /// <summary>
        /// 是否正在监控
        /// </summary>
        public bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// 最后心跳时间
        /// </summary>
        public DateTime LastHeartbeat => _lastHeartbeat;

        /// <summary>
        /// 心跳间隔（秒）
        /// </summary>
        public int HeartbeatInterval 
        { 
            get => _heartbeatInterval; 
            set => _heartbeatInterval = Math.Max(60, value); // 最小1分钟
        }
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化会话管理器
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        public ETOASessionManager(ETOAApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
            ETLogManager.Info("ETOASessionManager初始化完成");
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 心跳回调方法
        /// </summary>
        /// <param name="state">状态对象</param>
        private async void HeartbeatCallback(object state)
        {
            try
            {
                var isValid = await IsSessionValidAsync();
                _lastHeartbeat = DateTime.Now;

                if (!isValid)
                {
                    // 会话无效，尝试刷新
                    await RefreshSessionAsync();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("心跳检查失败", ex);
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 开始会话监控
        /// </summary>
        /// <returns>启动是否成功</returns>
        public async Task<bool> StartSessionMonitoringAsync()
        {
            try
            {
                if (_isMonitoring)
                {
                    return true; // 已经在监控中
                }

                // 首次检查会话状态
                var isValid = await IsSessionValidAsync();
                if (!isValid)
                {
                    return false;
                }

                // 启动定时器
                _heartbeatTimer = new Timer(
                    HeartbeatCallback,
                    null,
                    TimeSpan.FromSeconds(_heartbeatInterval),
                    TimeSpan.FromSeconds(_heartbeatInterval)
                );

                _isMonitoring = true;
                _lastHeartbeat = DateTime.Now;
                ETLogManager.Info("会话监控启动成功");
                return true;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("启动会话监控失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止会话监控
        /// </summary>
        public void StopSessionMonitoring()
        {
            _heartbeatTimer?.Dispose();
            _heartbeatTimer = null;
            _isMonitoring = false;
        }

        /// <summary>
        /// 刷新会话
        /// </summary>
        /// <returns>刷新是否成功</returns>
        public async Task<bool> RefreshSessionAsync()
        {
            try
            {
                // 发送刷新请求到服务器
                // 这里需要根据具体OA系统的刷新机制实现
                var response = await _apiClient.GetAsync<object>("/api/session/refresh");
                return response != null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"刷新会话失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查会话是否有效
        /// </summary>
        /// <returns>会话是否有效</returns>
        public async Task<bool> IsSessionValidAsync()
        {
            try
            {
                if (!_apiClient.IsAuthenticated)
                {
                    return false;
                }

                // 发送验证请求到服务器
                // 这里需要根据具体OA系统的验证机制实现
                var response = await _apiClient.GetAsync<object>("/api/session/validate");
                return response != null;
            }
            catch (Exception)
            {
                return false;
            }
        }
        #endregion
    }
}
