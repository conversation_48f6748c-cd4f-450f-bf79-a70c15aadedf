using System;
using System.Collections.Generic;
using System.IO;
using ExtensionsTools.ETOAAutomation.Models;
using ET;

namespace ExtensionsTools.ETOAAutomation.Storage
{
    /// <summary>
    /// 认证信息存储类，负责安全存储和管理用户认证信息
    /// </summary>
    public class ETOAAuthStorage
    {
        #region 私有字段
        private readonly ETIniFile _configFile;
        private readonly string _configPath;
        private const string AUTH_SECTION = "Authentication";
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化认证信息存储
        /// </summary>
        public ETOAAuthStorage()
        {
            try
            {
                // 创建配置文件路径
                string configDir = Path.Combine(
                    Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location),
                    "Config");
                
                if (!Directory.Exists(configDir))
                {
                    Directory.CreateDirectory(configDir);
                }

                _configPath = Path.Combine(configDir, "ETOAAuth.ini");
                _configFile = new ETIniFile(_configPath);
                
                ETLogManager.Info($"认证存储初始化完成，配置文件: {_configPath}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("认证存储初始化失败", ex);
                throw new ETException("认证存储初始化失败", "ETOAAuthStorage", ex);
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 保存认证信息
        /// </summary>
        /// <param name="loginInfo">登录信息</param>
        /// <param name="username">用户名（用作存储键）</param>
        public void SaveAuthInfo(ETOALoginInfo loginInfo, string username)
        {
            try
            {
                if (loginInfo == null || string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("登录信息和用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                
                // 保存基本信息
                _configFile.IniWriteValue(userSection, "Username", loginInfo.Username);
                _configFile.IniWriteValue(userSection, "UserId", loginInfo.UserId);
                _configFile.IniWriteValue(userSection, "DisplayName", loginInfo.DisplayName);
                _configFile.IniWriteValue(userSection, "Token", loginInfo.Token);
                _configFile.IniWriteValue(userSection, "SessionId", loginInfo.SessionId);
                _configFile.IniWriteValue(userSection, "BaseUrl", loginInfo.BaseUrl);
                _configFile.IniWriteValue(userSection, "LoginUrl", loginInfo.LoginUrl);
                _configFile.IniWriteValue(userSection, "RedirectUrl", loginInfo.RedirectUrl);
                _configFile.IniWriteValue(userSection, "LoginTime", loginInfo.LoginTime.ToString("yyyy-MM-dd HH:mm:ss"));
                _configFile.IniWriteValue(userSection, "IsSuccess", loginInfo.IsSuccess.ToString());

                // 保存Cookie信息
                if (loginInfo.Cookies != null && loginInfo.Cookies.Count > 0)
                {
                    string cookieSection = $"{userSection}_Cookies";
                    foreach (var cookie in loginInfo.Cookies)
                    {
                        _configFile.IniWriteValue(cookieSection, cookie.Key, cookie.Value);
                    }
                }

                // 保存请求头信息
                if (loginInfo.Headers != null && loginInfo.Headers.Count > 0)
                {
                    string headerSection = $"{userSection}_Headers";
                    foreach (var header in loginInfo.Headers)
                    {
                        _configFile.IniWriteValue(headerSection, header.Key, header.Value);
                    }
                }

                ETLogManager.Info($"认证信息保存成功，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"保存认证信息失败，用户: {username}", ex);
                throw new ETException($"保存认证信息失败: {ex.Message}", "SaveAuthInfo", ex);
            }
        }

        /// <summary>
        /// 加载认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>登录信息</returns>
        public ETOALoginInfo LoadAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                
                // 检查用户配置是否存在
                string savedUsername = _configFile.IniReadValue(userSection, "Username", "");
                if (string.IsNullOrEmpty(savedUsername))
                {
                    ETLogManager.Warn($"未找到用户认证信息: {username}");
                    return null;
                }

                var loginInfo = new ETOALoginInfo
                {
                    Username = _configFile.IniReadValue(userSection, "Username", ""),
                    UserId = _configFile.IniReadValue(userSection, "UserId", ""),
                    DisplayName = _configFile.IniReadValue(userSection, "DisplayName", ""),
                    Token = _configFile.IniReadValue(userSection, "Token", ""),
                    SessionId = _configFile.IniReadValue(userSection, "SessionId", ""),
                    BaseUrl = _configFile.IniReadValue(userSection, "BaseUrl", ""),
                    LoginUrl = _configFile.IniReadValue(userSection, "LoginUrl", ""),
                    RedirectUrl = _configFile.IniReadValue(userSection, "RedirectUrl", ""),
                    IsSuccess = bool.Parse(_configFile.IniReadValue(userSection, "IsSuccess", "false"))
                };

                // 解析登录时间
                string loginTimeStr = _configFile.IniReadValue(userSection, "LoginTime", "");
                if (DateTime.TryParse(loginTimeStr, out DateTime loginTime))
                {
                    loginInfo.LoginTime = loginTime;
                }

                // 加载Cookie信息
                string cookieSection = $"{userSection}_Cookies";
                var cookieKeys = _configFile.GetKeys(cookieSection);
                if (cookieKeys != null)
                {
                    foreach (string key in cookieKeys)
                    {
                        string value = _configFile.IniReadValue(cookieSection, key, "");
                        if (!string.IsNullOrEmpty(value))
                        {
                            loginInfo.SetCookie(key, value);
                        }
                    }
                }

                // 加载请求头信息
                string headerSection = $"{userSection}_Headers";
                var headerKeys = _configFile.GetKeys(headerSection);
                if (headerKeys != null)
                {
                    foreach (string key in headerKeys)
                    {
                        string value = _configFile.IniReadValue(headerSection, key, "");
                        if (!string.IsNullOrEmpty(value))
                        {
                            loginInfo.SetHeader(key, value);
                        }
                    }
                }

                ETLogManager.Info($"认证信息加载成功，用户: {username}");
                return loginInfo;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"加载认证信息失败，用户: {username}", ex);
                throw new ETException($"加载认证信息失败: {ex.Message}", "LoadAuthInfo", ex);
            }
        }

        /// <summary>
        /// 删除认证信息
        /// </summary>
        /// <param name="username">用户名</param>
        public void DeleteAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    throw new ArgumentException("用户名不能为空");
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                string cookieSection = $"{userSection}_Cookies";
                string headerSection = $"{userSection}_Headers";

                // 删除所有相关配置节
                _configFile.DeleteSection(userSection);
                _configFile.DeleteSection(cookieSection);
                _configFile.DeleteSection(headerSection);

                ETLogManager.Info($"认证信息删除成功，用户: {username}");
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"删除认证信息失败，用户: {username}", ex);
                throw new ETException($"删除认证信息失败: {ex.Message}", "DeleteAuthInfo", ex);
            }
        }

        /// <summary>
        /// 获取所有已保存的用户名列表
        /// </summary>
        /// <returns>用户名列表</returns>
        public List<string> GetSavedUsernames()
        {
            try
            {
                var usernames = new List<string>();
                var sections = _configFile.GetSections();
                
                if (sections != null)
                {
                    foreach (string section in sections)
                    {
                        if (section.StartsWith(AUTH_SECTION + "_") && 
                            !section.Contains("_Cookies") && 
                            !section.Contains("_Headers"))
                        {
                            string username = section.Substring((AUTH_SECTION + "_").Length);
                            usernames.Add(username);
                        }
                    }
                }

                ETLogManager.Debug($"获取已保存用户列表，共 {usernames.Count} 个用户");
                return usernames;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("获取已保存用户列表失败", ex);
                return new List<string>();
            }
        }

        /// <summary>
        /// 检查用户认证信息是否存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <returns>是否存在</returns>
        public bool HasAuthInfo(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    return false;
                }

                string userSection = $"{AUTH_SECTION}_{username}";
                string savedUsername = _configFile.IniReadValue(userSection, "Username", "");
                return !string.IsNullOrEmpty(savedUsername);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"检查认证信息存在性失败，用户: {username}", ex);
                return false;
            }
        }

        /// <summary>
        /// 清理过期的认证信息
        /// </summary>
        /// <param name="expireDays">过期天数</param>
        public void CleanExpiredAuthInfo(int expireDays = 30)
        {
            try
            {
                var usernames = GetSavedUsernames();
                var expireTime = DateTime.Now.AddDays(-expireDays);
                int cleanedCount = 0;

                foreach (string username in usernames)
                {
                    string userSection = $"{AUTH_SECTION}_{username}";
                    string loginTimeStr = _configFile.IniReadValue(userSection, "LoginTime", "");
                    
                    if (DateTime.TryParse(loginTimeStr, out DateTime loginTime))
                    {
                        if (loginTime < expireTime)
                        {
                            DeleteAuthInfo(username);
                            cleanedCount++;
                        }
                    }
                }

                ETLogManager.Info($"清理过期认证信息完成，清理了 {cleanedCount} 个过期用户");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清理过期认证信息失败", ex);
            }
        }
        #endregion
    }
}
