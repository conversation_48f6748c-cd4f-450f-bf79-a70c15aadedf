using System;
using System.Collections.Generic;

namespace ExtensionsTools.ETOAAutomation.Models
{
    /// <summary>
    /// 登录信息模型
    /// </summary>
    public class ETOALoginInfo
    {
        /// <summary>
        /// 登录是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 登录时间
        /// </summary>
        public DateTime LoginTime { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户显示名称
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 认证Token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// 会话ID
        /// </summary>
        public string SessionId { get; set; }

        /// <summary>
        /// Cookie信息
        /// </summary>
        public Dictionary<string, string> Cookies { get; set; }

        /// <summary>
        /// 请求头信息
        /// </summary>
        public Dictionary<string, string> Headers { get; set; }

        /// <summary>
        /// OA系统基础URL
        /// </summary>
        public string BaseUrl { get; set; }

        /// <summary>
        /// 登录页面URL
        /// </summary>
        public string LoginUrl { get; set; }

        /// <summary>
        /// 登录后跳转的URL
        /// </summary>
        public string RedirectUrl { get; set; }

        /// <summary>
        /// 用户权限列表
        /// </summary>
        public List<string> Permissions { get; set; }

        /// <summary>
        /// 用户角色列表
        /// </summary>
        public List<string> Roles { get; set; }

        /// <summary>
        /// 登录错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 扩展属性
        /// </summary>
        public Dictionary<string, object> ExtendedProperties { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ETOALoginInfo()
        {
            Cookies = new Dictionary<string, string>();
            Headers = new Dictionary<string, string>();
            Permissions = new List<string>();
            Roles = new List<string>();
            ExtendedProperties = new Dictionary<string, object>();
            LoginTime = DateTime.Now;
        }

        /// <summary>
        /// 检查登录信息是否有效
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return IsSuccess && 
                   !string.IsNullOrEmpty(Token) && 
                   !string.IsNullOrEmpty(SessionId) &&
                   Cookies.Count > 0;
        }

        /// <summary>
        /// 获取指定名称的Cookie值
        /// </summary>
        /// <param name="cookieName">Cookie名称</param>
        /// <returns>Cookie值</returns>
        public string GetCookie(string cookieName)
        {
            return Cookies.ContainsKey(cookieName) ? Cookies[cookieName] : null;
        }

        /// <summary>
        /// 设置Cookie值
        /// </summary>
        /// <param name="cookieName">Cookie名称</param>
        /// <param name="cookieValue">Cookie值</param>
        public void SetCookie(string cookieName, string cookieValue)
        {
            if (Cookies.ContainsKey(cookieName))
            {
                Cookies[cookieName] = cookieValue;
            }
            else
            {
                Cookies.Add(cookieName, cookieValue);
            }
        }

        /// <summary>
        /// 获取指定名称的请求头值
        /// </summary>
        /// <param name="headerName">请求头名称</param>
        /// <returns>请求头值</returns>
        public string GetHeader(string headerName)
        {
            return Headers.ContainsKey(headerName) ? Headers[headerName] : null;
        }

        /// <summary>
        /// 设置请求头值
        /// </summary>
        /// <param name="headerName">请求头名称</param>
        /// <param name="headerValue">请求头值</param>
        public void SetHeader(string headerName, string headerValue)
        {
            if (Headers.ContainsKey(headerName))
            {
                Headers[headerName] = headerValue;
            }
            else
            {
                Headers.Add(headerName, headerValue);
            }
        }

        /// <summary>
        /// 检查用户是否具有指定权限
        /// </summary>
        /// <param name="permission">权限名称</param>
        /// <returns>是否具有权限</returns>
        public bool HasPermission(string permission)
        {
            return Permissions.Contains(permission);
        }

        /// <summary>
        /// 检查用户是否具有指定角色
        /// </summary>
        /// <param name="role">角色名称</param>
        /// <returns>是否具有角色</returns>
        public bool HasRole(string role)
        {
            return Roles.Contains(role);
        }

        /// <summary>
        /// 获取扩展属性值
        /// </summary>
        /// <typeparam name="T">属性值类型</typeparam>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public T GetExtendedProperty<T>(string propertyName)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                return (T)ExtendedProperties[propertyName];
            }
            return default(T);
        }

        /// <summary>
        /// 设置扩展属性值
        /// </summary>
        /// <param name="propertyName">属性名称</param>
        /// <param name="propertyValue">属性值</param>
        public void SetExtendedProperty(string propertyName, object propertyValue)
        {
            if (ExtendedProperties.ContainsKey(propertyName))
            {
                ExtendedProperties[propertyName] = propertyValue;
            }
            else
            {
                ExtendedProperties.Add(propertyName, propertyValue);
            }
        }
    }
}
