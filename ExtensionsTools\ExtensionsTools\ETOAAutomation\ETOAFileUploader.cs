using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Flurl.Http;
using ExtensionsTools.ETOAAutomation.Models;

namespace ExtensionsTools.ETOAAutomation
{
    /// <summary>
    /// 文件上传处理器，支持单文件和批量文件上传
    /// </summary>
    public class ETOAFileUploader
    {
        #region 私有字段
        private readonly ETOAApiClient _apiClient;
        private Action<int> _progressCallback;
        #endregion

        #region 公共属性
        /// <summary>
        /// 最大文件大小（MB）
        /// </summary>
        public int MaxFileSize { get; set; } = 100;

        /// <summary>
        /// 允许的文件扩展名
        /// </summary>
        public string[] AllowedExtensions { get; set; } = 
        {
            ".jpg", ".jpeg", ".png", ".gif", ".bmp",
            ".pdf", ".doc", ".docx", ".xls", ".xlsx",
            ".ppt", ".pptx", ".txt", ".zip", ".rar"
        };
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化文件上传器
        /// </summary>
        /// <param name="apiClient">API客户端</param>
        public ETOAFileUploader(ETOAApiClient apiClient)
        {
            _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 验证文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        private (bool IsValid, string ErrorMessage) ValidateFile(string filePath)
        {
            if (!File.Exists(filePath))
            {
                return (false, "文件不存在");
            }

            var fileInfo = new FileInfo(filePath);
            
            // 检查文件大小
            if (fileInfo.Length > MaxFileSize * 1024 * 1024)
            {
                return (false, $"文件大小超过限制 ({MaxFileSize}MB)");
            }

            // 检查文件扩展名
            var extension = fileInfo.Extension.ToLower();
            if (AllowedExtensions != null && AllowedExtensions.Length > 0)
            {
                bool isAllowed = false;
                foreach (var allowedExt in AllowedExtensions)
                {
                    if (extension == allowedExt.ToLower())
                    {
                        isAllowed = true;
                        break;
                    }
                }

                if (!isAllowed)
                {
                    return (false, $"不支持的文件类型: {extension}");
                }
            }

            return (true, null);
        }

        /// <summary>
        /// 创建上传结果
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="success">是否成功</param>
        /// <param name="message">消息</param>
        /// <param name="fileId">文件ID</param>
        /// <returns>上传结果</returns>
        private ETOAUploadResult CreateUploadResult(string filePath, bool success, string message, string fileId = null)
        {
            return new ETOAUploadResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                IsSuccess = success,
                Message = message,
                FileId = fileId,
                UploadTime = DateTime.Now
            };
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 设置进度回调
        /// </summary>
        /// <param name="progressCallback">进度回调方法</param>
        public void SetProgressCallback(Action<int> progressCallback)
        {
            _progressCallback = progressCallback;
        }

        /// <summary>
        /// 上传单个文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePath">文件路径</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果</returns>
        public async Task<ETOAUploadResult> UploadFileAsync(string endpoint, string filePath, object formData = null)
        {
            try
            {
                // 验证文件
                var validation = ValidateFile(filePath);
                if (!validation.IsValid)
                {
                    return CreateUploadResult(filePath, false, validation.ErrorMessage);
                }

                // 准备上传
                var fileName = Path.GetFileName(filePath);
                var fileBytes = await File.ReadAllBytesAsync(filePath);

                // 创建多部分表单数据
                var request = _apiClient.BaseUrl
                    .AppendPathSegment(endpoint)
                    .PostMultipartAsync(mp =>
                    {
                        mp.AddFile("file", fileBytes, fileName);
                        
                        // 添加表单数据
                        if (formData != null)
                        {
                            var properties = formData.GetType().GetProperties();
                            foreach (var prop in properties)
                            {
                                var value = prop.GetValue(formData);
                                if (value != null)
                                {
                                    mp.AddString(prop.Name, value.ToString());
                                }
                            }
                        }
                    });

                var response = await request;
                var result = await response.GetJsonAsync<dynamic>();

                // 报告进度
                _progressCallback?.Invoke(100);

                return CreateUploadResult(filePath, true, "上传成功", result?.fileId?.ToString());
            }
            catch (Exception ex)
            {
                return CreateUploadResult(filePath, false, $"上传失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量上传文件
        /// </summary>
        /// <param name="endpoint">上传端点</param>
        /// <param name="filePaths">文件路径数组</param>
        /// <param name="formData">表单数据</param>
        /// <returns>上传结果列表</returns>
        public async Task<List<ETOAUploadResult>> UploadFilesAsync(string endpoint, string[] filePaths, object formData = null)
        {
            var results = new List<ETOAUploadResult>();
            
            if (filePaths == null || filePaths.Length == 0)
            {
                return results;
            }

            for (int i = 0; i < filePaths.Length; i++)
            {
                var filePath = filePaths[i];
                var result = await UploadFileAsync(endpoint, filePath, formData);
                results.Add(result);

                // 报告总体进度
                var progress = (int)((i + 1) * 100.0 / filePaths.Length);
                _progressCallback?.Invoke(progress);
            }

            return results;
        }
        #endregion
    }
}
