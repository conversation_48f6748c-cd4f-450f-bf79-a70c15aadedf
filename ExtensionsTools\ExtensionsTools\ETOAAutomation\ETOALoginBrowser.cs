using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using CefSharp;
using CefSharp.WinForms;
using ExtensionsTools.ETOAAutomation.Models;

namespace ExtensionsTools.ETOAAutomation
{
    /// <summary>
    /// 基于CefSharp的OA系统登录浏览器，专门处理登录认证
    /// 窗体文件：包含ETOALoginBrowser.cs、ETOALoginBrowser.Designer.cs、ETOALoginBrowser.resx
    /// </summary>
    public partial class ETOALoginBrowser : Form
    {
        #region 私有字段
        private ChromiumWebBrowser _browser;
        private ETOALoginInfo _loginInfo;
        private bool _isLoginSuccessful = false;
        private Dictionary<string, string> _cookies = new Dictionary<string, string>();
        #endregion

        #region 公共属性
        /// <summary>
        /// 登录URL
        /// </summary>
        public string LoginUrl { get; set; }

        /// <summary>
        /// 登录是否成功
        /// </summary>
        public bool IsLoginSuccessful => _isLoginSuccessful;

        /// <summary>
        /// Cookie信息
        /// </summary>
        public Dictionary<string, string> Cookies => _cookies;

        /// <summary>
        /// CefSharp浏览器控件
        /// </summary>
        public ChromiumWebBrowser Browser => _browser;

        /// <summary>
        /// 关闭按钮
        /// </summary>
        public Button BtnClose { get; private set; }

        /// <summary>
        /// 状态标签
        /// </summary>
        public Label LblStatus { get; private set; }

        /// <summary>
        /// 进度条
        /// </summary>
        public ProgressBar ProgressBar { get; private set; }
        #endregion

        #region 构造函数
        /// <summary>
        /// 初始化登录浏览器
        /// </summary>
        /// <param name="loginUrl">登录URL</param>
        public ETOALoginBrowser(string loginUrl)
        {
            LoginUrl = loginUrl ?? throw new ArgumentNullException(nameof(loginUrl));
            InitializeComponent();
            InitializeBrowser();
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化浏览器
        /// </summary>
        private void InitializeBrowser()
        {
            _browser = new ChromiumWebBrowser(LoginUrl)
            {
                Dock = DockStyle.Fill
            };

            // 添加浏览器到窗体
            this.Controls.Add(_browser);

            // 绑定事件
            _browser.LoadingStateChanged += Browser_LoadingStateChanged;
            _browser.AddressChanged += Browser_AddressChanged;
        }

        /// <summary>
        /// 浏览器加载状态改变事件
        /// </summary>
        private void Browser_LoadingStateChanged(object sender, LoadingStateChangedEventArgs e)
        {
            if (!e.IsLoading)
            {
                // 页面加载完成，检查是否登录成功
                CheckLoginStatus();
            }
        }

        /// <summary>
        /// 浏览器地址改变事件
        /// </summary>
        private void Browser_AddressChanged(object sender, AddressChangedEventArgs e)
        {
            // 更新状态显示
            if (LblStatus != null)
            {
                this.Invoke(new Action(() =>
                {
                    LblStatus.Text = $"当前页面: {e.Address}";
                }));
            }
        }

        /// <summary>
        /// 检查登录状态
        /// </summary>
        private async void CheckLoginStatus()
        {
            try
            {
                // 获取Cookie信息
                var cookieManager = Cef.GetGlobalCookieManager();
                // 这里需要实现Cookie获取逻辑

                // 检查是否登录成功（根据URL变化或页面内容判断）
                var currentUrl = _browser.Address;
                if (!currentUrl.Contains("/login") && !currentUrl.Contains("/error"))
                {
                    _isLoginSuccessful = true;
                    await ExtractLoginInfo();
                }
            }
            catch (Exception ex)
            {
                // 记录错误
                System.Diagnostics.Debug.WriteLine($"检查登录状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取登录信息
        /// </summary>
        private async Task ExtractLoginInfo()
        {
            try
            {
                _loginInfo = new ETOALoginInfo
                {
                    IsSuccess = _isLoginSuccessful,
                    LoginTime = DateTime.Now,
                    Cookies = new Dictionary<string, string>(_cookies),
                    BaseUrl = LoginUrl
                };

                // 提取更多认证信息（Token、Headers等）
                // 这里需要根据具体OA系统实现
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"提取登录信息失败: {ex.Message}");
            }
        }
        #endregion

        #region 公共方法
        /// <summary>
        /// 显示登录对话框并获取认证信息
        /// </summary>
        /// <returns>登录信息</returns>
        public async Task<ETOALoginInfo> ShowLoginDialogAsync()
        {
            var tcs = new TaskCompletionSource<ETOALoginInfo>();

            this.FormClosed += (s, e) =>
            {
                tcs.SetResult(_loginInfo);
            };

            this.ShowDialog();
            return await tcs.Task;
        }

        /// <summary>
        /// 自动登录
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        public async Task<bool> AutoLoginAsync(string username, string password)
        {
            try
            {
                // 等待页面加载完成
                while (_browser.IsLoading)
                {
                    await Task.Delay(100);
                }

                // 执行自动填写登录表单的JavaScript
                var script = $@"
                    var usernameField = document.querySelector('input[name=""username""], input[type=""text""]');
                    var passwordField = document.querySelector('input[name=""password""], input[type=""password""]');
                    var submitButton = document.querySelector('input[type=""submit""], button[type=""submit""]');
                    
                    if (usernameField) usernameField.value = '{username}';
                    if (passwordField) passwordField.value = '{password}';
                    if (submitButton) submitButton.click();
                ";

                await _browser.EvaluateScriptAsync(script);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动登录失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 获取登录信息
        /// </summary>
        /// <returns>登录信息</returns>
        public ETOALoginInfo GetLoginInfo()
        {
            return _loginInfo;
        }

        /// <summary>
        /// 关闭浏览器
        /// </summary>
        public new void Close()
        {
            _browser?.Dispose();
            base.Close();
        }
        #endregion
    }
}
